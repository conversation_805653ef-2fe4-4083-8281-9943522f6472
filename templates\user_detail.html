{% extends "base.html" %}

{% block title %}用户详情{% endblock %}

{% block content %}
<div class="card">
    <div class="card-header">
        <h1 class="card-title">用户详情</h1>
        <p>查看用户 "{{ user.real_name }}" 的详细信息</p>
    </div>
    
    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 2rem;">
        <div>
            <h3>基本信息</h3>
            <table class="table">
                <tr>
                    <td><strong>用户ID</strong></td>
                    <td>{{ user.id }}</td>
                </tr>
                <tr>
                    <td><strong>用户名</strong></td>
                    <td>{{ user.username }}</td>
                </tr>
                <tr>
                    <td><strong>真实姓名</strong></td>
                    <td>{{ user.real_name }}</td>
                </tr>
                <tr>
                    <td><strong>角色</strong></td>
                    <td>
                        {% if user.role == 'admin' %}
                            <span style="color: var(--primary-color); font-weight: bold;">管理员</span>
                        {% else %}
                            <span style="color: var(--info-color); font-weight: bold;">员工</span>
                        {% endif %}
                    </td>
                </tr>
                <tr>
                    <td><strong>科室</strong></td>
                    <td>{{ user.department_name or '-' }}</td>
                </tr>
                <tr>
                    <td><strong>状态</strong></td>
                    <td>
                        {% if user.status == 'active' %}
                            <span style="color: var(--accent-color);">活跃</span>
                        {% else %}
                            <span style="color: var(--error-color);">禁用</span>
                        {% endif %}
                    </td>
                </tr>
            </table>
        </div>
        
        <div>
            <h3>联系信息</h3>
            <table class="table">
                <tr>
                    <td><strong>邮箱</strong></td>
                    <td>{{ user.email or '-' }}</td>
                </tr>
                <tr>
                    <td><strong>电话</strong></td>
                    <td>{{ user.phone or '-' }}</td>
                </tr>
                <tr>
                    <td><strong>创建时间</strong></td>
                    <td>{{ user.created_at.strftime('%Y-%m-%d %H:%M:%S') if user.created_at else '-' }}</td>
                </tr>
                <tr>
                    <td><strong>更新时间</strong></td>
                    <td>{{ user.updated_at.strftime('%Y-%m-%d %H:%M:%S') if user.updated_at else '-' }}</td>
                </tr>
            </table>
        </div>
    </div>
    
    <div class="card-actions">
        <a href="{{ url_for('user.edit_user', user_id=user.id) }}" class="btn btn-primary">编辑用户</a>
        <a href="{{ url_for('user.user_list') }}" class="btn btn-secondary">返回列表</a>
        
        <!-- 状态切换按钮 -->
        {% if user.status == 'active' %}
        <button type="button" class="btn btn-warning" onclick="toggleUserStatus({{ user.id }}, '禁用')">禁用用户</button>
        {% else %}
        <button type="button" class="btn btn-success" onclick="toggleUserStatus({{ user.id }}, '启用')">启用用户</button>
        {% endif %}
        
        <!-- 重置密码按钮 -->
        <button type="button" class="btn btn-info" onclick="showResetPasswordModal({{ user.id }}, '{{ user.real_name }}')">重置密码</button>
        
        <!-- 删除按钮 -->
        <button type="button" class="btn btn-danger" onclick="confirmDeleteUser({{ user.id }}, '{{ user.real_name }}')">删除用户</button>
    </div>
</div>

<!-- 重置密码模态框 -->
<div id="resetPasswordModal" class="modal" style="display: none;">
    <div class="modal-content">
        <div class="modal-header">
            <h3>重置密码</h3>
            <span class="close" onclick="hideResetPasswordModal()">&times;</span>
        </div>
        <div class="modal-body">
            <form id="resetPasswordForm" method="POST">
                <p>为用户 <strong id="resetUserName"></strong> 重置密码：</p>
                <div class="form-group">
                    <label for="newPassword" class="form-label">新密码</label>
                    <input type="password" id="newPassword" name="new_password" class="form-control" required>
                </div>
                <div class="form-group">
                    <label for="confirmPassword" class="form-label">确认密码</label>
                    <input type="password" id="confirmPassword" class="form-control" required>
                </div>
                <div class="form-actions">
                    <button type="submit" class="btn btn-primary">重置密码</button>
                    <button type="button" class="btn btn-secondary" onclick="hideResetPasswordModal()">取消</button>
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// 用户状态切换
function toggleUserStatus(userId, action) {
    if (confirm('确定要' + action + '该用户吗？')) {
        var form = document.createElement('form');
        form.method = 'POST';
        form.action = '/users/' + userId + '/toggle-status';
        document.body.appendChild(form);
        form.submit();
    }
}

// 删除用户确认
function confirmDeleteUser(userId, userName) {
    if (confirm('确定要删除用户 "' + userName + '" 吗？\n\n注意：删除后无法恢复！')) {
        var form = document.createElement('form');
        form.method = 'POST';
        form.action = '/users/' + userId + '/delete';
        document.body.appendChild(form);
        form.submit();
    }
}

// 显示重置密码模态框
function showResetPasswordModal(userId, userName) {
    document.getElementById('resetUserName').textContent = userName;
    document.getElementById('resetPasswordForm').action = '/users/' + userId + '/reset-password';
    document.getElementById('resetPasswordModal').style.display = 'block';
}

// 隐藏重置密码模态框
function hideResetPasswordModal() {
    document.getElementById('resetPasswordModal').style.display = 'none';
    document.getElementById('resetPasswordForm').reset();
}

// 密码确认验证
document.addEventListener('DOMContentLoaded', function() {
    document.getElementById('resetPasswordForm').addEventListener('submit', function(e) {
        var newPassword = document.getElementById('newPassword').value;
        var confirmPassword = document.getElementById('confirmPassword').value;
        
        if (newPassword !== confirmPassword) {
            e.preventDefault();
            alert('两次输入的密码不一致！');
            return false;
        }
        
        if (newPassword.length < 6) {
            e.preventDefault();
            alert('密码长度至少6位！');
            return false;
        }
    });
});
</script>
{% endblock %}
