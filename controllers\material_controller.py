from flask import Blueprint, request, render_template, redirect, url_for, session, flash, jsonify
from controllers.auth_controller import login_required, admin_required, get_current_user
from services.material_service import MaterialService
from services.auth_service import AuthService
from dao.user_dao import UserDAO
from dao.department_dao import DepartmentDAO
from models.user import User
from datetime import datetime, date

material_bp = Blueprint('material', __name__)
material_service = MaterialService()
auth_service = AuthService()
user_dao = UserDAO()
department_dao = DepartmentDAO()

@material_bp.route('/dashboard')
@login_required
def dashboard():
    """仪表板"""
    user_info = get_current_user()
    user = User.from_dict(user_info)
    
    try:
        # 获取物资统计
        materials = material_service.get_material_list(user)
        
        # 获取待审核申请（仅管理员）
        pending_requests = []
        if user.is_admin():
            pending_requests = material_service.get_pending_requests(user)
        
        # 获取最近分配记录
        recent_allocations = material_service.get_allocation_history(user)[:10]
        
        stats = {
            'total_materials': len(materials),
            'fixed_assets': len([m for m in materials if m['category'] == 'fixed_asset']),
            'consumables': len([m for m in materials if m['category'] == 'consumable']),
            'pending_requests': len(pending_requests)
        }
        
        return render_template('dashboard.html', 
                             user=user_info, 
                             stats=stats,
                             recent_allocations=recent_allocations,
                             pending_requests=pending_requests)
    except Exception as e:
        flash(f'加载仪表板失败：{str(e)}', 'error')
        return render_template('dashboard.html', user=user_info)

@material_bp.route('/materials')
@login_required
def material_list():
    """物资列表"""
    user_info = get_current_user()
    user = User.from_dict(user_info)

    # 获取查询参数
    category = request.args.get('category')
    keyword = request.args.get('keyword')
    min_price = request.args.get('min_price', type=float)
    max_price = request.args.get('max_price', type=float)
    department_id = request.args.get('department_id', type=int)

    try:
        materials = material_service.get_material_list(user, category, keyword, min_price, max_price, department_id)

        # 获取科室列表（仅管理员）
        departments = []
        if user.is_admin():
            from dao.department_dao import DepartmentDAO
            department_dao = DepartmentDAO()
            departments = department_dao.get_all_departments()

        return render_template('material_list.html',
                             materials=materials,
                             departments=departments,
                             user=user_info,
                             filters={'category': category, 'keyword': keyword,
                                    'min_price': min_price, 'max_price': max_price,
                                    'department_id': department_id})
    except Exception as e:
        flash(f'获取物资列表失败：{str(e)}', 'error')
        return render_template('material_list.html',
                             materials=[],
                             departments=[],
                             user=user_info,
                             filters={'category': category, 'keyword': keyword,
                                    'min_price': min_price, 'max_price': max_price,
                                    'department_id': department_id})

@material_bp.route('/materials/add', methods=['GET', 'POST'])
@admin_required
def add_material():
    """添加物资"""
    if request.method == 'POST':
        try:
            material_data = {
                'name': request.form.get('name'),
                'model': request.form.get('model'),
                'category': request.form.get('category'),
                'price': float(request.form.get('price', 0)),
                'purchase_date': datetime.strptime(request.form.get('purchase_date'), '%Y-%m-%d').date(),
                'supplier': request.form.get('supplier'),
                'purchase_amount': float(request.form.get('purchase_amount', 0)),
                'quantity': int(request.form.get('quantity', 1)),
                'description': request.form.get('description')
            }
            
            user_info = get_current_user()
            user = User.from_dict(user_info)
            
            material = material_service.create_material(material_data, user)
            flash(f'物资 "{material.name}" 添加成功', 'success')
            return redirect(url_for('material.material_list'))
            
        except Exception as e:
            flash(f'添加物资失败：{str(e)}', 'error')
    
    return render_template('add_material.html')

@material_bp.route('/materials/<int:material_id>')
@login_required
def material_detail(material_id):
    """物资详情"""
    user_info = get_current_user()
    user = User.from_dict(user_info)
    
    try:
        material = material_service.get_material_detail(material_id, user)
        if not material:
            flash('物资不存在', 'error')
            return redirect(url_for('material.material_list'))
        
        return render_template('material_detail.html', material=material, user=user_info)
    except Exception as e:
        flash(f'获取物资详情失败：{str(e)}', 'error')
        return redirect(url_for('material.material_list'))

@material_bp.route('/materials/<int:material_id>/allocate', methods=['GET', 'POST'])
@admin_required
def allocate_material(material_id):
    """分配物资"""
    if request.method == 'POST':
        try:
            department_id = int(request.form.get('department_id'))
            user_id = request.form.get('user_id')
            user_id = int(user_id) if user_id else None
            quantity = int(request.form.get('quantity', 1))
            notes = request.form.get('notes')
            
            user_info = get_current_user()
            
            material_service.allocate_material(
                material_id, department_id, user_id, quantity, notes, user_info['id']
            )
            
            flash('物资分配成功', 'success')
            return redirect(url_for('material.material_detail', material_id=material_id))
            
        except Exception as e:
            flash(f'分配物资失败：{str(e)}', 'error')
    
    # 获取科室和用户列表
    departments = department_dao.get_all_departments()
    users = user_dao.get_all_users()
    
    return render_template('allocate_material.html', 
                         material_id=material_id,
                         departments=departments,
                         users=users)

@material_bp.route('/materials/<int:material_id>/request', methods=['POST'])
@login_required
def request_material(material_id):
    """申请物资"""
    try:
        quantity = int(request.form.get('quantity', 1))
        reason = request.form.get('reason')
        
        user_info = get_current_user()
        user = User.from_dict(user_info)
        
        material_service.request_material(material_id, quantity, reason, user)
        flash('物资申请提交成功，等待管理员审核', 'success')
        
    except Exception as e:
        flash(f'申请物资失败：{str(e)}', 'error')
    
    return redirect(url_for('material.material_detail', material_id=material_id))

@material_bp.route('/requests')
@admin_required
def request_list():
    """申请列表"""
    user_info = get_current_user()
    user = User.from_dict(user_info)
    
    try:
        requests = material_service.get_pending_requests(user)
        return render_template('request_list.html', requests=requests, user=user_info)
    except Exception as e:
        flash(f'获取申请列表失败：{str(e)}', 'error')
        return render_template('request_list.html', requests=[], user=user_info)

@material_bp.route('/requests/<int:request_id>/approve', methods=['POST'])
@admin_required
def approve_request(request_id):
    """批准申请"""
    try:
        notes = request.form.get('notes')
        user_info = get_current_user()
        user = User.from_dict(user_info)
        
        material_service.approve_request(request_id, user, notes)
        flash('申请已批准', 'success')
    except Exception as e:
        flash(f'批准申请失败：{str(e)}', 'error')
    
    return redirect(url_for('material.request_list'))

@material_bp.route('/requests/<int:request_id>/reject', methods=['POST'])
@admin_required
def reject_request(request_id):
    """拒绝申请"""
    try:
        notes = request.form.get('notes')
        user_info = get_current_user()
        user = User.from_dict(user_info)

        material_service.reject_request(request_id, user, notes)
        flash('申请已拒绝', 'success')
    except Exception as e:
        flash(f'拒绝申请失败：{str(e)}', 'error')

    return redirect(url_for('material.request_list'))

@material_bp.route('/materials/<int:material_id>/delete', methods=['POST'])
@login_required
def delete_material(material_id):
    """删除物资"""
    try:
        user_info = get_current_user()
        user = User.from_dict(user_info)

        material_service.delete_material(material_id, user)
        flash('物资删除成功', 'success')
    except Exception as e:
        flash(f'删除物资失败：{str(e)}', 'error')

    return redirect(url_for('material.material_list'))

@material_bp.route('/materials/<int:material_id>/edit', methods=['GET', 'POST'])
@admin_required
def edit_material(material_id):
    """编辑物资"""
    if request.method == 'POST':
        try:
            material_data = {
                'name': request.form.get('name'),
                'model': request.form.get('model'),
                'category': request.form.get('category'),
                'price': float(request.form.get('price', 0)),
                'purchase_date': datetime.strptime(request.form.get('purchase_date'), '%Y-%m-%d').date(),
                'supplier': request.form.get('supplier'),
                'purchase_amount': float(request.form.get('purchase_amount', 0)),
                'quantity': int(request.form.get('quantity', 1)),
                'remaining_quantity': int(request.form.get('remaining_quantity', 1)),
                'status': request.form.get('status'),
                'description': request.form.get('description')
            }

            material_service.update_material(material_id, material_data, User.from_dict(get_current_user()))
            flash('物资信息更新成功', 'success')
            return redirect(url_for('material.material_detail', material_id=material_id))

        except Exception as e:
            flash(f'更新物资失败：{str(e)}', 'error')

    # 获取物资信息
    try:
        user_info = get_current_user()
        user = User.from_dict(user_info)
        material = material_service.get_material_detail(material_id, user)
        if not material:
            flash('物资不存在', 'error')
            return redirect(url_for('material.material_list'))

        return render_template('edit_material.html', material=material, user=user_info)
    except Exception as e:
        flash(f'获取物资信息失败：{str(e)}', 'error')
        return redirect(url_for('material.material_list'))

@material_bp.route('/materials/batch-delete', methods=['POST'])
@admin_required
def batch_delete_materials():
    """批量删除物资"""
    try:
        material_ids = request.form.getlist('material_ids')
        if not material_ids:
            flash('请选择要删除的物资', 'error')
            return redirect(url_for('material.material_list'))

        user_info = get_current_user()
        user = User.from_dict(user_info)

        success_count = 0
        error_count = 0

        for material_id in material_ids:
            try:
                material_service.delete_material(int(material_id), user)
                success_count += 1
            except Exception as e:
                error_count += 1
                print(f"删除物资 {material_id} 失败: {e}")

        if success_count > 0:
            flash(f'成功删除 {success_count} 个物资', 'success')
        if error_count > 0:
            flash(f'{error_count} 个物资删除失败', 'error')

    except Exception as e:
        flash(f'批量删除失败：{str(e)}', 'error')

    return redirect(url_for('material.material_list'))

@material_bp.route('/materials/batch-import', methods=['POST'])
@admin_required
def batch_import_materials():
    """批量导入物资"""
    try:
        if 'file' not in request.files:
            flash('请选择要导入的文件', 'error')
            return redirect(url_for('material.material_list'))

        file = request.files['file']
        if file.filename == '':
            flash('请选择要导入的文件', 'error')
            return redirect(url_for('material.material_list'))

        if not file.filename.lower().endswith(('.xlsx', '.xls')):
            flash('请选择Excel文件（.xlsx或.xls格式）', 'error')
            return redirect(url_for('material.material_list'))

        user_info = get_current_user()
        user = User.from_dict(user_info)

        result = material_service.import_materials_from_excel(file, user)

        if result['success_count'] > 0:
            flash(f'成功导入 {result["success_count"]} 个物资', 'success')
        if result['error_count'] > 0:
            flash(f'{result["error_count"]} 个物资导入失败', 'error')
        if result['errors']:
            for error in result['errors'][:5]:  # 只显示前5个错误
                flash(f'第{error["row"]}行: {error["message"]}', 'error')

    except Exception as e:
        flash(f'导入失败：{str(e)}', 'error')

    return redirect(url_for('material.material_list'))

@material_bp.route('/allocations')
@login_required
def allocation_history():
    """分配历史"""
    user_info = get_current_user()
    user = User.from_dict(user_info)
    
    department_id = request.args.get('department_id', type=int)
    
    try:
        allocations = material_service.get_allocation_history(user, department_id)
        departments = auth_service.get_accessible_departments(user)
        
        return render_template('allocation_history.html', 
                             allocations=allocations, 
                             departments=departments,
                             user=user_info)
    except Exception as e:
        flash(f'获取分配历史失败：{str(e)}', 'error')
        return render_template('allocation_history.html', 
                             allocations=[],
                             departments=[],
                             user=user_info)

@material_bp.route('/allocations/<int:allocation_id>/delete', methods=['POST'])
@admin_required
def delete_allocation(allocation_id):
    """删除分配记录"""
    try:
        user_info = get_current_user()
        user = User.from_dict(user_info)

        material_service.delete_allocation(allocation_id, user)
        flash('分配记录删除成功', 'success')
    except Exception as e:
        flash(f'删除分配记录失败：{str(e)}', 'error')

    return redirect(url_for('material.allocation_history'))
