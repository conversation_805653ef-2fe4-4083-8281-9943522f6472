from datetime import datetime

class User:
    def __init__(self, id=None, username=None, password=None, real_name=None, 
                 role='employee', department_id=None, email=None, phone=None, 
                 status='active', created_at=None, updated_at=None):
        self.id = id
        self.username = username
        self.password = password
        self.real_name = real_name
        self.role = role  # 'admin' or 'employee'
        self.department_id = department_id
        self.email = email
        self.phone = phone
        self.status = status  # 'active' or 'inactive'
        self.created_at = created_at or datetime.now()
        self.updated_at = updated_at or datetime.now()
        
    def is_admin(self):
        """检查是否为管理员"""
        return self.role == 'admin'
    
    def is_active(self):
        """检查用户是否激活"""
        # 如果没有status字段，默认为激活状态
        return getattr(self, 'status', 'active') == 'active'

    def to_dict(self):
        """转换为字典格式"""
        return {
            'id': self.id,
            'username': self.username,
            'real_name': self.real_name,
            'role': self.role,
            'department_id': self.department_id,
            'email': self.email,
            'phone': self.phone,
            'status': self.status,
            'created_at': self.created_at,
            'updated_at': self.updated_at
        }
    
    def to_dict(self):
        """转换为字典格式"""
        return {
            'id': self.id,
            'username': self.username,
            'real_name': self.real_name,
            'role': self.role,
            'department_id': self.department_id,
            'email': self.email,
            'phone': self.phone,
            'status': self.status,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None
        }
    
    @classmethod
    def from_dict(cls, data):
        """从字典创建User对象"""
        return cls(
            id=data.get('id'),
            username=data.get('username'),
            password=data.get('password'),  # 数据库字段名是password
            real_name=data.get('real_name'),
            role=data.get('role', 'employee'),
            department_id=data.get('department_id'),
            email=data.get('email'),
            phone=data.get('phone'),
            status=data.get('status', 'active'),
            created_at=data.get('created_at'),
            updated_at=data.get('updated_at')
        )
