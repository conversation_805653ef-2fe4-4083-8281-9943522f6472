{% extends "base.html" %}

{% block title %}AI智能助手{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <h4 class="mb-0">
                        <i class="fas fa-robot me-2"></i>AI智能助手 - 小智
                    </h4>
                </div>
                <div class="card-body p-0">
                    <!-- 聊天区域 -->
                    <div id="chatContainer" class="chat-container">
                        <div id="chatMessages" class="chat-messages">
                            <!-- 欢迎消息 -->
                            <div class="message bot-message">
                                <div class="message-avatar">
                                    <i class="fas fa-robot"></i>
                                </div>
                                <div class="message-content">
                                    <div class="message-text">
                                        您好！我是物资管理系统的智能助手小智 🤖<br>
                                        我可以帮助您：<br>
                                        • 了解系统功能和使用方法<br>
                                        • 解答物资申请相关问题<br>
                                        • 提供操作指导和帮助<br><br>
                                        有什么问题尽管问我吧！
                                    </div>
                                    <div class="message-time"></div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- 快速回复按钮 -->
                        <div id="quickReplies" class="quick-replies">
                            <button class="btn btn-outline-primary btn-sm quick-reply-btn" data-message="请告诉我如何申请物资">
                                如何申请物资？
                            </button>
                            <button class="btn btn-outline-primary btn-sm quick-reply-btn" data-message="我想查询申请状态">
                                申请状态查询
                            </button>
                            <button class="btn btn-outline-primary btn-sm quick-reply-btn" data-message="请介绍系统的主要功能">
                                系统使用帮助
                            </button>
                            <button class="btn btn-outline-primary btn-sm quick-reply-btn" data-message="如何联系系统管理员">
                                联系管理员
                            </button>
                        </div>
                    </div>
                    
                    <!-- 输入区域 -->
                    <div class="chat-input-container">
                        <div class="input-group">
                            <input type="text" id="messageInput" class="form-control" 
                                   placeholder="输入您的问题..." maxlength="500">
                            <button class="btn btn-primary" type="button" id="sendButton">
                                <i class="fas fa-paper-plane"></i>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 加载指示器 -->
<div id="loadingIndicator" class="loading-indicator" style="display: none;">
    <div class="spinner-border text-primary" role="status">
        <span class="visually-hidden">正在思考...</span>
    </div>
    <span class="ms-2">小智正在思考...</span>
</div>
{% endblock %}

{% block extra_css %}
<style>
.chat-container {
    height: 600px;
    display: flex;
    flex-direction: column;
}

.chat-messages {
    flex: 1;
    overflow-y: auto;
    padding: 20px;
    background-color: #f8f9fa;
}

.message {
    display: flex;
    margin-bottom: 20px;
    animation: fadeIn 0.3s ease-in;
}

.message-avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 12px;
    font-size: 18px;
}

.bot-message .message-avatar {
    background-color: #007bff;
    color: white;
}

.user-message {
    flex-direction: row-reverse;
}

.user-message .message-avatar {
    background-color: #28a745;
    color: white;
    margin-right: 0;
    margin-left: 12px;
}

.message-content {
    max-width: 70%;
    min-width: 100px;
}

.message-text {
    background-color: white;
    padding: 12px 16px;
    border-radius: 18px;
    box-shadow: 0 1px 2px rgba(0,0,0,0.1);
    word-wrap: break-word;
}

.user-message .message-text {
    background-color: #007bff;
    color: white;
}

.message-time {
    font-size: 12px;
    color: #6c757d;
    margin-top: 4px;
    padding: 0 16px;
}

.quick-replies {
    padding: 15px 20px;
    border-top: 1px solid #dee2e6;
    background-color: white;
}

.quick-reply-btn {
    margin: 2px;
    border-radius: 20px;
}

.chat-input-container {
    padding: 20px;
    background-color: white;
    border-top: 1px solid #dee2e6;
}

.loading-indicator {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background-color: rgba(255, 255, 255, 0.95);
    padding: 20px;
    border-radius: 10px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    z-index: 1000;
    display: flex;
    align-items: center;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
}

.typing-indicator {
    display: flex;
    align-items: center;
    padding: 12px 16px;
    background-color: #e9ecef;
    border-radius: 18px;
    margin-bottom: 10px;
}

.typing-dots {
    display: flex;
    gap: 4px;
}

.typing-dot {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background-color: #6c757d;
    animation: typing 1.4s infinite ease-in-out;
}

.typing-dot:nth-child(1) { animation-delay: -0.32s; }
.typing-dot:nth-child(2) { animation-delay: -0.16s; }

@keyframes typing {
    0%, 80%, 100% { transform: scale(0.8); opacity: 0.5; }
    40% { transform: scale(1); opacity: 1; }
}
</style>
{% endblock %}

{% block extra_js %}
<script>
class Chatbot {
    constructor() {
        this.messageInput = document.getElementById('messageInput');
        this.sendButton = document.getElementById('sendButton');
        this.chatMessages = document.getElementById('chatMessages');
        this.loadingIndicator = document.getElementById('loadingIndicator');
        this.conversationHistory = [];
        
        this.initEventListeners();
        this.scrollToBottom();
    }
    
    initEventListeners() {
        // 发送按钮点击事件
        this.sendButton.addEventListener('click', () => this.sendMessage());
        
        // 输入框回车事件
        this.messageInput.addEventListener('keypress', (e) => {
            if (e.key === 'Enter' && !e.shiftKey) {
                e.preventDefault();
                this.sendMessage();
            }
        });
        
        // 快速回复按钮事件
        document.querySelectorAll('.quick-reply-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                const message = e.target.getAttribute('data-message');
                this.sendQuickReply(message);
            });
        });
    }
    
    async sendMessage() {
        const message = this.messageInput.value.trim();
        if (!message) return;
        
        // 显示用户消息
        this.addMessage(message, 'user');
        this.messageInput.value = '';
        
        // 显示加载指示器
        this.showLoading();
        
        try {
            // 发送请求到后端
            const response = await fetch('/api/chat', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    message: message,
                    history: this.conversationHistory
                })
            });
            
            const result = await response.json();
            
            if (result.success) {
                // 显示AI回复
                this.addMessage(result.reply, 'bot');
                
                // 更新对话历史
                this.conversationHistory.push(
                    { role: 'user', content: message },
                    { role: 'assistant', content: result.reply }
                );
                
                // 限制历史记录长度
                if (this.conversationHistory.length > 20) {
                    this.conversationHistory = this.conversationHistory.slice(-20);
                }
            } else {
                this.addMessage(result.reply || '抱歉，服务暂时不可用。', 'bot');
            }
        } catch (error) {
            console.error('发送消息失败:', error);
            this.addMessage('抱歉，网络连接异常，请稍后重试。', 'bot');
        } finally {
            this.hideLoading();
        }
    }
    
    sendQuickReply(message) {
        this.messageInput.value = message;
        this.sendMessage();
    }
    
    addMessage(text, sender) {
        const messageDiv = document.createElement('div');
        messageDiv.className = `message ${sender}-message`;
        
        const avatarDiv = document.createElement('div');
        avatarDiv.className = 'message-avatar';
        avatarDiv.innerHTML = sender === 'user' ? '<i class="fas fa-user"></i>' : '<i class="fas fa-robot"></i>';
        
        const contentDiv = document.createElement('div');
        contentDiv.className = 'message-content';
        
        const textDiv = document.createElement('div');
        textDiv.className = 'message-text';
        textDiv.innerHTML = text.replace(/\n/g, '<br>');
        
        const timeDiv = document.createElement('div');
        timeDiv.className = 'message-time';
        timeDiv.textContent = new Date().toLocaleTimeString();
        
        contentDiv.appendChild(textDiv);
        contentDiv.appendChild(timeDiv);
        
        messageDiv.appendChild(avatarDiv);
        messageDiv.appendChild(contentDiv);
        
        this.chatMessages.appendChild(messageDiv);
        this.scrollToBottom();
    }
    
    showLoading() {
        this.loadingIndicator.style.display = 'flex';
        this.sendButton.disabled = true;
    }
    
    hideLoading() {
        this.loadingIndicator.style.display = 'none';
        this.sendButton.disabled = false;
    }
    
    scrollToBottom() {
        setTimeout(() => {
            this.chatMessages.scrollTop = this.chatMessages.scrollHeight;
        }, 100);
    }
}

// 初始化聊天机器人
document.addEventListener('DOMContentLoaded', () => {
    new Chatbot();
});
</script>
{% endblock %}
