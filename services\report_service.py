from dao.material_dao import MaterialDAO
from dao.allocation_dao import AllocationDAO
from dao.department_dao import DepartmentDAO
from dao.user_dao import UserDAO
import openpyxl
from openpyxl.styles import Font, Alignment, PatternFill
from datetime import datetime, date
import io

class ReportService:
    def __init__(self):
        self.material_dao = MaterialDAO()
        self.allocation_dao = AllocationDAO()
        self.department_dao = DepartmentDAO()
        self.user_dao = UserDAO()
    
    def get_material_statistics(self, user):
        """获取物资统计信息"""
        if user.is_admin():
            # 管理员可以查看全部统计
            stats = self.material_dao.get_material_statistics()
            dept_stats = self.department_dao.get_department_statistics()
            
            return {
                'material_stats': stats,
                'department_stats': dept_stats,
                'total_value': sum(item['total_value'] or 0 for item in stats),
                'total_materials': sum(item['total_count'] or 0 for item in stats)
            }
        else:
            # 普通员工只能查看本科室统计
            return self._get_department_statistics(user.department_id)
    
    def get_category_statistics(self, user):
        """获取分类统计"""
        stats = self.material_dao.get_material_statistics()
        
        if not user.is_admin():
            # 普通员工需要过滤数据
            dept_materials = self.allocation_dao.get_allocations_by_department(user.department_id)
            dept_material_ids = {item['material_id'] for item in dept_materials}
            # 这里需要进一步过滤统计数据，简化处理
        
        result = {
            'fixed_asset': {'count': 0, 'value': 0},
            'consumable': {'count': 0, 'value': 0}
        }
        
        for stat in stats:
            category = stat['category']
            if category in result:
                result[category]['count'] = stat['total_count'] or 0
                result[category]['value'] = stat['total_value'] or 0
        
        return result
    
    def get_department_material_report(self, department_id, user):
        """获取科室物资报表"""
        if not user.is_admin() and user.department_id != department_id:
            raise PermissionError("无权查看其他科室数据")

        allocations = self.allocation_dao.get_allocations_by_department(department_id)
        department = self.department_dao.get_department_by_id(department_id)

        # 计算汇总信息
        summary = self._calculate_department_summary(allocations)

        # 获取科室用户数量
        users = self.user_dao.get_users_by_department(department_id)

        # 为每个分配记录添加 total_value 字段
        enhanced_allocations = []
        for allocation in allocations:
            allocation_dict = dict(allocation) if isinstance(allocation, dict) else allocation
            unit_price = allocation_dict.get('unit_price', 0) or 0
            quantity = allocation_dict.get('quantity', 1) or 1
            allocation_dict['total_value'] = float(unit_price) * float(quantity)
            enhanced_allocations.append(allocation_dict)

        return {
            'department': department.to_dict() if department else None,
            'department_name': department.name if department else '未知科室',
            'total_users': len(users),
            'total_materials': len(allocations),
            'total_value': summary['total_value'],
            'allocations': enhanced_allocations,
            'summary': summary
        }
    
    def generate_material_inventory_excel(self, user, category=None):
        """生成物资台账Excel报表"""
        if category:
            materials = self.material_dao.get_materials_by_category(category)
        else:
            materials = self.material_dao.get_all_materials()
        
        # 如果是普通员工，过滤数据
        if not user.is_admin():
            dept_materials = self.allocation_dao.get_allocations_by_department(user.department_id)
            dept_material_ids = {item['material_id'] for item in dept_materials}
            materials = [m for m in materials if m.id in dept_material_ids]
        
        # 创建Excel工作簿
        wb = openpyxl.Workbook()
        ws = wb.active
        ws.title = "物资台账"
        
        # 设置标题
        title = f"物资台账报表 - {category if category else '全部'}"
        ws.merge_cells('A1:J1')
        ws['A1'] = title
        ws['A1'].font = Font(size=16, bold=True)
        ws['A1'].alignment = Alignment(horizontal='center')
        
        # 设置表头
        headers = ['序号', '资产编号', '物资名称', '型号', '类别', '单价', '数量', '剩余数量', '状态', '购入日期']
        for col, header in enumerate(headers, 1):
            cell = ws.cell(row=3, column=col, value=header)
            cell.font = Font(bold=True)
            cell.fill = PatternFill(start_color='CCCCCC', end_color='CCCCCC', fill_type='solid')
        
        # 填充数据
        for row, material in enumerate(materials, 4):
            ws.cell(row=row, column=1, value=row-3)
            ws.cell(row=row, column=2, value=material.asset_number or '-')
            ws.cell(row=row, column=3, value=material.name)
            ws.cell(row=row, column=4, value=material.model or '-')
            ws.cell(row=row, column=5, value='固定资产' if material.category == 'fixed_asset' else '耗材')
            ws.cell(row=row, column=6, value=material.price)
            ws.cell(row=row, column=7, value=material.quantity)
            ws.cell(row=row, column=8, value=material.remaining_quantity)
            ws.cell(row=row, column=9, value=self._get_status_text(material.status))
            ws.cell(row=row, column=10, value=material.purchase_date.strftime('%Y-%m-%d') if material.purchase_date else '-')
        
        # 调整列宽
        for col in range(1, 11):
            ws.column_dimensions[openpyxl.utils.get_column_letter(col)].width = 15
        
        # 保存到内存
        output = io.BytesIO()
        wb.save(output)
        output.seek(0)
        
        return output

    def generate_department_report_excel(self, user, department_id):
        """生成科室物资报表Excel"""
        if not user.is_admin() and user.department_id != department_id:
            raise PermissionError("无权查看其他科室数据")

        # 获取科室报表数据
        report_data = self.get_department_material_report(department_id, user)

        # 创建工作簿
        wb = openpyxl.Workbook()
        ws = wb.active
        ws.title = "科室物资报表"

        # 设置标题
        dept_name = report_data.get('department_name', '未知科室')
        ws['A1'] = f"{dept_name} - 物资分配报表"
        ws.merge_cells('A1:H1')

        # 设置标题样式
        title_font = Font(size=16, bold=True)
        title_alignment = Alignment(horizontal='center', vertical='center')
        ws['A1'].font = title_font
        ws['A1'].alignment = title_alignment

        # 添加汇总信息
        row = 3
        ws[f'A{row}'] = f"科室名称: {dept_name}"
        ws[f'D{row}'] = f"科室人数: {report_data.get('total_users', 0)}"
        row += 1
        ws[f'A{row}'] = f"分配物资数: {report_data.get('total_materials', 0)}"
        ws[f'D{row}'] = f"物资总价值: ¥{report_data.get('total_value', 0):.2f}"

        # 设置表头
        row = 6
        headers = ['物资名称', '分类', '分配用户', '数量', '单价', '总价值', '分配日期', '状态']
        for col, header in enumerate(headers, 1):
            cell = ws.cell(row=row, column=col, value=header)
            cell.font = Font(bold=True)
            cell.fill = PatternFill(start_color='CCCCCC', end_color='CCCCCC', fill_type='solid')

        # 填充数据
        row += 1
        for allocation in report_data.get('allocations', []):
            ws.cell(row=row, column=1, value=allocation.get('material_name', ''))
            ws.cell(row=row, column=2, value='固定资产' if allocation.get('category') == 'fixed_asset' else '消耗品')
            ws.cell(row=row, column=3, value=allocation.get('user_name', '科室公用'))
            ws.cell(row=row, column=4, value=allocation.get('quantity', 1))
            ws.cell(row=row, column=5, value=f"¥{allocation.get('unit_price', 0):.2f}")
            ws.cell(row=row, column=6, value=f"¥{allocation.get('total_value', 0):.2f}")

            # 处理分配日期
            alloc_date = allocation.get('allocation_date') or allocation.get('allocated_at')
            if alloc_date:
                if hasattr(alloc_date, 'strftime'):
                    ws.cell(row=row, column=7, value=alloc_date.strftime('%Y-%m-%d'))
                else:
                    ws.cell(row=row, column=7, value=str(alloc_date))
            else:
                ws.cell(row=row, column=7, value='')

            # 状态
            status = allocation.get('status', 'active')
            status_text = '正常' if status == 'active' else ('已归还' if status == 'returned' else '其他')
            ws.cell(row=row, column=8, value=status_text)

            row += 1

        # 调整列宽
        for col in range(1, 9):
            ws.column_dimensions[openpyxl.utils.get_column_letter(col)].width = 15

        # 保存到内存
        output = io.BytesIO()
        wb.save(output)
        output.seek(0)

        return output

    def generate_allocation_report_excel(self, user, department_id=None):
        """生成分配报表Excel"""
        if department_id:
            if not user.is_admin() and user.department_id != department_id:
                raise PermissionError("无权查看其他科室数据")
            allocations = self.allocation_dao.get_allocations_by_department(department_id)
            title = f"科室物资分配报表"
        else:
            if not user.is_admin():
                allocations = self.allocation_dao.get_allocations_by_user(user.id)
                title = "个人物资分配报表"
            else:
                allocations = self.allocation_dao.get_all_allocations()
                title = "全部物资分配报表"
        
        # 创建Excel工作簿
        wb = openpyxl.Workbook()
        ws = wb.active
        ws.title = "分配报表"
        
        # 设置标题
        ws.merge_cells('A1:I1')
        ws['A1'] = title
        ws['A1'].font = Font(size=16, bold=True)
        ws['A1'].alignment = Alignment(horizontal='center')
        
        # 设置表头
        headers = ['序号', '物资名称', '类别', '科室', '领取人', '数量', '分配日期', '状态', '备注']
        for col, header in enumerate(headers, 1):
            cell = ws.cell(row=3, column=col, value=header)
            cell.font = Font(bold=True)
            cell.fill = PatternFill(start_color='CCCCCC', end_color='CCCCCC', fill_type='solid')
        
        # 填充数据
        for row, allocation in enumerate(allocations, 4):
            ws.cell(row=row, column=1, value=row-3)
            ws.cell(row=row, column=2, value=allocation.get('material_name', '-'))
            ws.cell(row=row, column=3, value='固定资产' if allocation.get('category') == 'fixed_asset' else '耗材')
            ws.cell(row=row, column=4, value=allocation.get('department_name', '-'))
            ws.cell(row=row, column=5, value=allocation.get('user_name', '-'))
            ws.cell(row=row, column=6, value=allocation.get('quantity', 0))
            ws.cell(row=row, column=7, value=allocation.get('allocation_date', '').strftime('%Y-%m-%d') if allocation.get('allocation_date') else '-')
            ws.cell(row=row, column=8, value=self._get_allocation_status_text(allocation.get('status')))
            ws.cell(row=row, column=9, value=allocation.get('notes', '-'))
        
        # 调整列宽
        for col in range(1, 10):
            ws.column_dimensions[openpyxl.utils.get_column_letter(col)].width = 15
        
        # 保存到内存
        output = io.BytesIO()
        wb.save(output)
        output.seek(0)
        
        return output
    
    def _get_department_statistics(self, department_id):
        """获取科室统计信息"""
        allocations = self.allocation_dao.get_allocations_by_department(department_id)
        
        stats = {
            'total_allocations': len(allocations),
            'fixed_asset_count': 0,
            'consumable_count': 0,
            'total_value': 0
        }
        
        for allocation in allocations:
            if allocation.get('category') == 'fixed_asset':
                stats['fixed_asset_count'] += 1
            else:
                stats['consumable_count'] += 1
        
        return stats
    
    def _calculate_department_summary(self, allocations):
        """计算科室汇总信息"""
        summary = {
            'total_items': len(allocations),
            'fixed_assets': 0,
            'consumables': 0,
            'active_allocations': 0,
            'total_value': 0.0
        }

        for allocation in allocations:
            if allocation.get('category') == 'fixed_asset':
                summary['fixed_assets'] += 1
            else:
                summary['consumables'] += 1

            if allocation.get('status') == 'allocated':
                summary['active_allocations'] += 1

            # 计算总价值
            unit_price = allocation.get('unit_price', 0) or 0
            quantity = allocation.get('quantity', 1) or 1
            summary['total_value'] += float(unit_price) * float(quantity)

        return summary
    
    def _get_status_text(self, status):
        """获取状态文本"""
        status_map = {
            'available': '可用',
            'in_use': '在用',
            'scrapped': '已报废'
        }
        return status_map.get(status, status)
    
    def _get_allocation_status_text(self, status):
        """获取分配状态文本"""
        status_map = {
            'allocated': '已分配',
            'returned': '已归还',
            'consumed': '已消耗'
        }
        return status_map.get(status, status)

    def get_advanced_statistics(self, user, category=None, department_id=None, start_date=None, end_date=None):
        """获取高级统计数据"""
        # 基础物资统计
        material_stats = self.material_dao.get_material_statistics()

        # 按科室统计分配情况
        department_allocations = {}
        if user.is_admin():
            departments = self.department_dao.get_all_departments()
            for dept in departments:
                # 处理Department对象
                dept_id = dept.id if hasattr(dept, 'id') else dept['id']
                dept_name = dept.name if hasattr(dept, 'name') else dept['name']

                if department_id and dept_id != department_id:
                    continue

                allocations = self.allocation_dao.get_allocations_by_department(dept_id)

                # 按类别过滤
                if category:
                    allocations = [a for a in allocations if a.get('category') == category]

                # 按日期过滤
                if start_date or end_date:
                    filtered_allocations = []
                    for allocation in allocations:
                        alloc_date = allocation.get('allocated_date')
                        if alloc_date:
                            if isinstance(alloc_date, str):
                                from datetime import datetime
                                alloc_date = datetime.strptime(alloc_date, '%Y-%m-%d').date()

                            if start_date and alloc_date < datetime.strptime(start_date, '%Y-%m-%d').date():
                                continue
                            if end_date and alloc_date > datetime.strptime(end_date, '%Y-%m-%d').date():
                                continue
                        filtered_allocations.append(allocation)
                    allocations = filtered_allocations

                department_allocations[dept_name] = {
                    'total_allocations': len(allocations),
                    'fixed_assets': len([a for a in allocations if a.get('category') == 'fixed_asset']),
                    'consumables': len([a for a in allocations if a.get('category') == 'consumable']),
                    'allocations': allocations[:10]  # 最近10条记录
                }

        # 按类别统计
        category_summary = {
            'fixed_asset': {'count': 0, 'value': 0, 'allocated': 0},
            'consumable': {'count': 0, 'value': 0, 'allocated': 0}
        }

        for stat in material_stats:
            cat = stat['category']
            if cat in category_summary:
                category_summary[cat]['count'] = stat['total_count'] or 0
                category_summary[cat]['value'] = float(stat['total_value'] or 0)

        # 统计已分配数量
        all_allocations = self.allocation_dao.get_all_allocations()
        for allocation in all_allocations:
            cat = allocation.get('category')
            if cat in category_summary:
                category_summary[cat]['allocated'] += 1

        return {
            'material_stats': material_stats,
            'department_allocations': department_allocations,
            'category_summary': category_summary,
            'total_departments': len(department_allocations),
            'filters_applied': bool(category or department_id or start_date or end_date)
        }
