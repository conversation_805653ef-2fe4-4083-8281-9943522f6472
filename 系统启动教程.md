# 物资管理系统启动教程

## 📋 系统要求

### 软件环境
- **Python**: 3.8 或以上版本
- **MySQL**: 8.0 或以上版本
- **操作系统**: Windows 10/11、Linux、macOS

### 硬件要求
- **内存**: 4GB 以上
- **硬盘**: 500MB 可用空间
- **网络**: 支持本地网络访问

## 🚀 快速启动步骤

### 第一步：环境准备

1. **安装Python依赖**
   ```bash
   pip install -r requirements.txt
   ```

2. **启动MySQL服务**
   - 确保MySQL服务正在运行
   - 数据库连接信息：
     - 主机: localhost
     - 用户: root
     - 密码: lax217652
     - 数据库: goods

### 第二步：数据库初始化

1. **初始化数据库**（首次运行）
   ```bash
   python init_database.py
   ```

2. **检查数据库连接**
   ```bash
   python check_db.py
   ```

### 第三步：启动系统

1. **启动Flask应用**
   ```bash
   python app.py
   ```

2. **访问系统**
   - 打开浏览器访问: http://127.0.0.1:5000
   - 或访问: http://localhost:5000

## 👤 默认登录账户

### 管理员账户
- **用户名**: admin
- **密码**: admin123
- **权限**: 完整管理权限

### 普通用户账户
- **用户名**: user1
- **密码**: user123
- **权限**: 基本用户权限

## 🔧 常见问题解决

### 问题1：数据库连接失败
**解决方案:**
```bash
# 检查MySQL服务状态
# Windows:
net start mysql

# Linux/macOS:
sudo systemctl start mysql
```

### 问题2：端口被占用
**解决方案:**
- 修改 `app.py` 文件中的端口号
- 或者终止占用5000端口的进程

### 问题3：依赖包安装失败
**解决方案:**
```bash
# 升级pip
pip install --upgrade pip

# 使用国内镜像源
pip install -r requirements.txt -i https://pypi.tuna.tsinghua.edu.cn/simple/
```

## 📊 系统功能概览

### 用户端功能
- ✅ 物资查询和浏览
- ✅ 物资申请提交
- ✅ 申请状态查看
- ✅ 个人申请历史

### 管理端功能
- ✅ 物资信息管理
- ✅ 申请审批处理
- ✅ 分配记录管理
- ✅ 统计报表生成
- ✅ 用户权限管理
- ✅ AI智能推荐

### AI智能代理
- 🤖 库存预警提醒
- 🤖 智能物资推荐
- 🤖 数据分析报告

### AI聊天机器人
- 🤖 24/7智能客服支持
- 🤖 物资申请流程指导
- 🤖 系统使用帮助
- 🤖 问题解答和故障排除

## 🛠️ 系统维护

### 日志查看
- 系统日志位置: `logs/agent.log`
- 包含系统运行状态和错误信息

### 数据备份
```bash
# 备份数据库
mysqldump -u root -p goods > backup.sql

# 恢复数据库
mysql -u root -p goods < backup.sql
```

### 重建数据库（谨慎操作）
```bash
python rebuild_database.py
```

## 📞 技术支持

如遇到问题，请检查：
1. Python版本是否符合要求
2. MySQL服务是否正常运行
3. 网络端口是否被占用
4. 依赖包是否完整安装

---

## 🤖 AI聊天机器人使用

### 访问AI助手
1. 登录系统后，点击导航栏中的"AI助手"
2. 或直接访问: http://127.0.0.1:5000/chatbot

### 功能特性
- **智能问答**: 回答物资管理相关问题
- **操作指导**: 提供系统使用帮助
- **快速回复**: 预设常见问题快速回复
- **实时响应**: 即时获得AI回复

### 常见问题示例
- "如何申请物资？"
- "申请状态查询"
- "系统使用帮助"
- "联系管理员"

---

**注意**:
- 首次启动系统时，AI智能代理会自动初始化并开始后台运行，提供智能化的物资管理服务
- AI聊天机器人当前使用演示模式，如需使用完整AI功能，请参考《AI聊天机器人使用说明.md》配置DeepSeek API密钥
