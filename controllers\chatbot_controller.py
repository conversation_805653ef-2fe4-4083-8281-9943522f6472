from flask import Blueprint, request, jsonify, session, render_template, redirect, url_for
from services.chatbot_service import chatbot_service
import logging

chatbot_bp = Blueprint('chatbot', __name__)
logger = logging.getLogger(__name__)

@chatbot_bp.route('/chatbot')
def chatbot_page():
    """聊天机器人页面"""
    if 'user_id' not in session:
        return redirect(url_for('auth.login'))
    
    return render_template('chatbot.html')

@chatbot_bp.route('/api/chat', methods=['POST'])
def chat():
    """处理聊天请求"""
    try:
        if 'user_id' not in session:
            return jsonify({
                "success": False,
                "error": "请先登录"
            }), 401
        
        data = request.get_json()
        if not data or 'message' not in data:
            return jsonify({
                "success": False,
                "error": "消息内容不能为空"
            }), 400
        
        message = data['message'].strip()
        if not message:
            return jsonify({
                "success": False,
                "error": "消息内容不能为空"
            }), 400
        
        # 获取对话历史
        conversation_history = data.get('history', [])
        
        # 调用聊天机器人服务
        result = chatbot_service.chat(message, conversation_history)
        
        return jsonify(result)
        
    except Exception as e:
        logger.error(f"聊天请求处理异常: {e}")
        return jsonify({
            "success": False,
            "error": "服务器内部错误"
        }), 500

@chatbot_bp.route('/api/chat/quick-replies', methods=['GET'])
def get_quick_replies():
    """获取快速回复选项"""
    try:
        if 'user_id' not in session:
            return jsonify({
                "success": False,
                "error": "请先登录"
            }), 401
        
        quick_replies = chatbot_service.get_quick_replies()
        return jsonify({
            "success": True,
            "quick_replies": quick_replies
        })
        
    except Exception as e:
        logger.error(f"获取快速回复异常: {e}")
        return jsonify({
            "success": False,
            "error": "服务器内部错误"
        }), 500

@chatbot_bp.route('/api/chat/system-info', methods=['GET'])
def get_system_info():
    """获取系统信息"""
    try:
        if 'user_id' not in session:
            return jsonify({
                "success": False,
                "error": "请先登录"
            }), 401
        
        system_info = chatbot_service.get_system_info()
        return jsonify({
            "success": True,
            "info": system_info
        })
        
    except Exception as e:
        logger.error(f"获取系统信息异常: {e}")
        return jsonify({
            "success": False,
            "error": "服务器内部错误"
        }), 500
