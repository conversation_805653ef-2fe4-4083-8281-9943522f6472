{% extends "base.html" %}

{% block title %}用户管理{% endblock %}

{% block content %}
<div class="card">
    <div class="card-header">
        <h1 class="card-title">用户管理</h1>
        <p>管理系统用户账户</p>
    </div>
    
    <!-- 搜索和筛选 -->
    <div class="card-body">
        <form method="GET" class="mb-3">
            <div class="row">
                <div class="col-md-3">
                    <input type="text" name="keyword" class="form-control" placeholder="搜索用户名、姓名或邮箱" value="{{ filters.keyword }}">
                </div>
                <div class="col-md-2">
                    <select name="role" class="form-control">
                        <option value="">所有角色</option>
                        <option value="admin" {% if filters.role == 'admin' %}selected{% endif %}>管理员</option>
                        <option value="employee" {% if filters.role == 'employee' %}selected{% endif %}>员工</option>
                    </select>
                </div>
                <div class="col-md-2">
                    <select name="department_id" class="form-control">
                        <option value="">所有科室</option>
                        {% for dept in departments %}
                        <option value="{{ dept.id }}" {% if filters.department_id == dept.id %}selected{% endif %}>{{ dept.name }}</option>
                        {% endfor %}
                    </select>
                </div>
                <div class="col-md-2">
                    <select name="status" class="form-control">
                        <option value="">所有状态</option>
                        <option value="active" {% if filters.status == 'active' %}selected{% endif %}>活跃</option>
                        <option value="inactive" {% if filters.status == 'inactive' %}selected{% endif %}>禁用</option>
                    </select>
                </div>
                <div class="col-md-3">
                    <button type="submit" class="btn btn-primary">搜索</button>
                    <a href="{{ url_for('user.user_list') }}" class="btn btn-secondary">重置</a>
                    <a href="{{ url_for('user.add_user') }}" class="btn btn-success">添加用户</a>
                </div>
            </div>
        </form>
    </div>
    
    <!-- 用户列表 -->
    {% if users %}
    <table class="table">
        <thead>
            <tr>
                <th>用户名</th>
                <th>姓名</th>
                <th>角色</th>
                <th>科室</th>
                <th>邮箱</th>
                <th>电话</th>
                <th>状态</th>
                <th>创建时间</th>
                <th>操作</th>
            </tr>
        </thead>
        <tbody>
            {% for user in users %}
            <tr>
                <td>{{ user.username }}</td>
                <td>{{ user.real_name }}</td>
                <td>
                    {% if user.role == 'admin' %}
                        <span style="color: var(--primary-color); font-weight: bold;">管理员</span>
                    {% else %}
                        <span style="color: var(--info-color); font-weight: bold;">员工</span>
                    {% endif %}
                </td>
                <td>{{ user.department_name }}</td>
                <td>{{ user.email or '-' }}</td>
                <td>{{ user.phone or '-' }}</td>
                <td>
                    {% if user.status == 'active' %}
                        <span style="color: var(--accent-color);">活跃</span>
                    {% else %}
                        <span style="color: var(--error-color);">禁用</span>
                    {% endif %}
                </td>
                <td>{{ user.created_at.strftime('%Y-%m-%d') if user.created_at else '-' }}</td>
                <td>
                    <div class="btn-group">
                        <a href="{{ url_for('user.user_detail', user_id=user.id) }}" class="btn btn-sm btn-info">详情</a>
                        <a href="{{ url_for('user.edit_user', user_id=user.id) }}" class="btn btn-sm btn-primary">编辑</a>
                        
                        <!-- 状态切换按钮 -->
                        {% if user.status == 'active' %}
                        <button type="button" class="btn btn-sm btn-warning" onclick="toggleUserStatus({{ user.id }}, '禁用')">禁用</button>
                        {% else %}
                        <button type="button" class="btn btn-sm btn-success" onclick="toggleUserStatus({{ user.id }}, '启用')">启用</button>
                        {% endif %}
                        
                        <!-- 重置密码按钮 -->
                        <button type="button" class="btn btn-sm btn-secondary" onclick="showResetPasswordModal({{ user.id }}, '{{ user.real_name }}')">重置密码</button>
                        
                        <!-- 删除按钮 -->
                        <button type="button" class="btn btn-sm btn-danger" onclick="confirmDeleteUser({{ user.id }}, '{{ user.real_name }}')">删除</button>
                    </div>
                </td>
            </tr>
            {% endfor %}
        </tbody>
    </table>
    {% else %}
    <div class="text-center py-4">
        <p>暂无用户数据</p>
    </div>
    {% endif %}
</div>

<!-- 重置密码模态框 -->
<div id="resetPasswordModal" class="modal" style="display: none;">
    <div class="modal-content">
        <div class="modal-header">
            <h3>重置密码</h3>
            <span class="close" onclick="hideResetPasswordModal()">&times;</span>
        </div>
        <div class="modal-body">
            <form id="resetPasswordForm" method="POST">
                <p>为用户 <strong id="resetUserName"></strong> 重置密码：</p>
                <div class="form-group">
                    <label for="newPassword" class="form-label">新密码</label>
                    <input type="password" id="newPassword" name="new_password" class="form-control" required>
                </div>
                <div class="form-group">
                    <label for="confirmPassword" class="form-label">确认密码</label>
                    <input type="password" id="confirmPassword" class="form-control" required>
                </div>
                <div class="form-actions">
                    <button type="submit" class="btn btn-primary">重置密码</button>
                    <button type="button" class="btn btn-secondary" onclick="hideResetPasswordModal()">取消</button>
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// 用户状态切换
function toggleUserStatus(userId, action) {
    if (confirm('确定要' + action + '该用户吗？')) {
        var form = document.createElement('form');
        form.method = 'POST';
        form.action = '/users/' + userId + '/toggle-status';
        document.body.appendChild(form);
        form.submit();
    }
}

// 删除用户确认
function confirmDeleteUser(userId, userName) {
    if (confirm('确定要删除用户 "' + userName + '" 吗？\n\n注意：删除后无法恢复！')) {
        var form = document.createElement('form');
        form.method = 'POST';
        form.action = '/users/' + userId + '/delete';
        document.body.appendChild(form);
        form.submit();
    }
}

// 显示重置密码模态框
function showResetPasswordModal(userId, userName) {
    document.getElementById('resetUserName').textContent = userName;
    document.getElementById('resetPasswordForm').action = '/users/' + userId + '/reset-password';
    document.getElementById('resetPasswordModal').style.display = 'block';
}

// 隐藏重置密码模态框
function hideResetPasswordModal() {
    document.getElementById('resetPasswordModal').style.display = 'none';
    document.getElementById('resetPasswordForm').reset();
}

// 密码确认验证
document.addEventListener('DOMContentLoaded', function() {
    document.getElementById('resetPasswordForm').addEventListener('submit', function(e) {
        var newPassword = document.getElementById('newPassword').value;
        var confirmPassword = document.getElementById('confirmPassword').value;

        if (newPassword !== confirmPassword) {
            e.preventDefault();
            alert('两次输入的密码不一致！');
            return false;
        }

        if (newPassword.length < 6) {
            e.preventDefault();
            alert('密码长度至少6位！');
            return false;
        }
    });
});
</script>
{% endblock %}
