#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
创建物资导入Excel模板文件
"""

import os
from datetime import datetime

def create_excel_template():
    """创建Excel模板文件"""
    try:
        import openpyxl
        from openpyxl.styles import Font, PatternFill, Alignment
    except ImportError:
        print("请先安装openpyxl库：pip install openpyxl")
        return
    
    # 创建工作簿
    workbook = openpyxl.Workbook()
    worksheet = workbook.active
    worksheet.title = "物资导入模板"
    
    # 设置列标题
    headers = [
        "物资名称*",
        "型号", 
        "类别*",
        "单价*",
        "购入日期",
        "供应商",
        "采购金额",
        "数量*",
        "描述"
    ]
    
    # 写入标题行
    for col, header in enumerate(headers, 1):
        cell = worksheet.cell(row=1, column=col, value=header)
        cell.font = Font(bold=True, color="FFFFFF")
        cell.fill = PatternFill(start_color="366092", end_color="366092", fill_type="solid")
        cell.alignment = Alignment(horizontal="center", vertical="center")
    
    # 添加示例数据
    sample_data = [
        ["办公桌", "DK-001", "fixed_asset", 800.00, "2024-01-15", "办公家具公司", 800.00, 1, "标准办公桌"],
        ["A4打印纸", "A4-80G", "consumable", 25.00, "2024-01-20", "办公用品店", 250.00, 10, "80克A4复印纸"],
        ["笔记本电脑", "ThinkPad-X1", "fixed_asset", 8500.00, "2024-01-10", "联想公司", 8500.00, 1, "商务笔记本电脑"]
    ]
    
    for row, data in enumerate(sample_data, 2):
        for col, value in enumerate(data, 1):
            worksheet.cell(row=row, column=col, value=value)
    
    # 设置列宽
    column_widths = [15, 12, 12, 10, 12, 15, 12, 8, 20]
    for col, width in enumerate(column_widths, 1):
        worksheet.column_dimensions[openpyxl.utils.get_column_letter(col)].width = width
    
    # 添加说明工作表
    instructions_sheet = workbook.create_sheet("导入说明")
    
    instructions = [
        ["物资批量导入说明", ""],
        ["", ""],
        ["1. 文件格式要求：", ""],
        ["   - 支持 .xlsx 和 .xls 格式", ""],
        ["   - 请使用提供的模板格式", ""],
        ["", ""],
        ["2. 字段说明：", ""],
        ["   物资名称*", "必填，物资的名称"],
        ["   型号", "可选，物资的型号规格"],
        ["   类别*", "必填，只能填写：fixed_asset（固定资产）或 consumable（耗材）"],
        ["   单价*", "必填，物资的单价，必须大于0"],
        ["   购入日期", "可选，格式：YYYY-MM-DD，如：2024-01-15"],
        ["   供应商", "可选，供应商名称"],
        ["   采购金额", "可选，如不填写将自动计算（单价×数量）"],
        ["   数量*", "必填，物资数量，必须大于0"],
        ["   描述", "可选，物资的详细描述"],
        ["", ""],
        ["3. 注意事项：", ""],
        ["   - 标有*的字段为必填项", ""],
        ["   - 类别只能填写 fixed_asset 或 consumable", ""],
        ["   - 日期格式必须为 YYYY-MM-DD", ""],
        ["   - 数量和单价必须为正数", ""],
        ["   - 建议先删除示例数据，再填入实际数据", ""],
        ["", ""],
        ["4. 导入流程：", ""],
        ["   - 填写完数据后保存文件", ""],
        ["   - 在系统中选择批量导入", ""],
        ["   - 上传Excel文件", ""],
        ["   - 系统会显示导入结果", ""]
    ]
    
    for row, (title, content) in enumerate(instructions, 1):
        instructions_sheet.cell(row=row, column=1, value=title)
        instructions_sheet.cell(row=row, column=2, value=content)
        
        if "说明" in title:
            cell = instructions_sheet.cell(row=row, column=1)
            cell.font = Font(bold=True, size=14)
        elif title.startswith("   "):
            cell = instructions_sheet.cell(row=row, column=1)
            cell.font = Font(bold=True)
    
    # 设置说明页列宽
    instructions_sheet.column_dimensions['A'].width = 20
    instructions_sheet.column_dimensions['B'].width = 50
    
    # 确保目录存在
    os.makedirs("static/template", exist_ok=True)
    
    # 保存文件
    filename = "static/template/物资导入模板.xlsx"
    workbook.save(filename)
    print(f"Excel模板文件已创建：{filename}")

if __name__ == "__main__":
    create_excel_template()
