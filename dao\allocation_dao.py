from dao.database import db
from models.allocation import MaterialAllocation, MaterialRequest
from datetime import datetime

class AllocationDAO:
    def __init__(self):
        self.db = db
    
    def create_allocation(self, allocation):
        """创建物资分配记录"""
        sql = """
        INSERT INTO material_allocations (material_id, department_id, user_id, allocated_by,
                                        quantity, allocation_date, status, notes)
        VALUES (%s, %s, %s, %s, %s, %s, %s, %s)
        """
        params = (allocation.material_id, allocation.department_id, allocation.user_id,
                 allocation.allocated_by, allocation.quantity, allocation.allocation_date,
                 allocation.status, allocation.notes)
        allocation_id = self.db.execute_insert(sql, params)
        allocation.id = allocation_id
        return allocation
    
    def get_allocation_by_id(self, allocation_id):
        """根据ID获取分配记录"""
        sql = "SELECT * FROM material_allocations WHERE id = %s"
        result = self.db.execute_query_one(sql, (allocation_id,))
        return MaterialAllocation.from_dict(result) if result else None
    
    def get_allocations_by_department(self, department_id):
        """根据科室获取分配记录"""
        sql = """
        SELECT ma.*, m.name as material_name, m.category, u.real_name as user_name
        FROM material_allocations ma
        LEFT JOIN materials m ON ma.material_id = m.id
        LEFT JOIN users u ON ma.user_id = u.id
        WHERE ma.department_id = %s
        ORDER BY ma.allocation_date DESC
        """
        return self.db.execute_query(sql, (department_id,))
    
    def get_allocations_by_user(self, user_id):
        """根据用户获取分配记录"""
        sql = """
        SELECT ma.*, m.name as material_name, m.category, d.name as department_name
        FROM material_allocations ma
        LEFT JOIN materials m ON ma.material_id = m.id
        LEFT JOIN departments d ON ma.department_id = d.id
        WHERE ma.user_id = %s
        ORDER BY ma.allocation_date DESC
        """
        return self.db.execute_query(sql, (user_id,))
    
    def get_all_allocations(self):
        """获取所有分配记录"""
        sql = """
        SELECT ma.*, m.name as material_name, m.category, 
               d.name as department_name, u.real_name as user_name,
               admin.real_name as allocated_by_name
        FROM material_allocations ma
        LEFT JOIN materials m ON ma.material_id = m.id
        LEFT JOIN departments d ON ma.department_id = d.id
        LEFT JOIN users u ON ma.user_id = u.id
        LEFT JOIN users admin ON ma.allocated_by = admin.id
        ORDER BY ma.allocation_date DESC
        """
        return self.db.execute_query(sql)
    
    def update_allocation_status(self, allocation_id, status, return_date=None):
        """更新分配状态"""
        if return_date:
            sql = """
            UPDATE material_allocations SET status = %s, return_date = %s, updated_at = %s
            WHERE id = %s
            """
            params = (status, return_date, datetime.now(), allocation_id)
        else:
            sql = """
            UPDATE material_allocations SET status = %s, updated_at = %s
            WHERE id = %s
            """
            params = (status, datetime.now(), allocation_id)
        return self.db.execute_update(sql, params)


class RequestDAO:
    def __init__(self):
        self.db = db
    
    def create_request(self, request):
        """创建物资申请"""
        sql = """
        INSERT INTO material_requests (material_id, user_id, department_id, quantity,
                                     request_date, status, reason)
        VALUES (%s, %s, %s, %s, %s, %s, %s)
        """
        params = (request.material_id, request.user_id, request.department_id,
                 request.quantity, request.request_date, request.status, request.reason)
        request_id = self.db.execute_insert(sql, params)
        request.id = request_id
        return request
    
    def get_request_by_id(self, request_id):
        """根据ID获取申请记录"""
        sql = "SELECT * FROM material_requests WHERE id = %s"
        result = self.db.execute_query_one(sql, (request_id,))
        return MaterialRequest.from_dict(result) if result else None
    
    def get_requests_by_user(self, user_id):
        """根据用户获取申请记录"""
        sql = """
        SELECT mr.*, m.name as material_name, m.category, d.name as department_name
        FROM material_requests mr
        LEFT JOIN materials m ON mr.material_id = m.id
        LEFT JOIN departments d ON mr.department_id = d.id
        WHERE mr.user_id = %s
        ORDER BY mr.request_date DESC
        """
        return self.db.execute_query(sql, (user_id,))
    
    def get_pending_requests(self):
        """获取待审核的申请"""
        sql = """
        SELECT mr.*, m.name as material_name, m.category,
               d.name as department_name, u.real_name as user_name
        FROM material_requests mr
        LEFT JOIN materials m ON mr.material_id = m.id
        LEFT JOIN users u ON mr.user_id = u.id
        LEFT JOIN departments d ON u.department_id = d.id
        WHERE mr.status = 'pending'
        ORDER BY mr.request_date ASC
        """
        return self.db.execute_query(sql)
    
    def approve_request(self, request_id, processed_by, notes=None):
        """批准申请"""
        sql = """
        UPDATE material_requests SET status = 'approved', processed_by = %s,
               processed_date = %s, notes = %s, updated_at = %s
        WHERE id = %s
        """
        params = (processed_by, datetime.now().date(), notes, datetime.now(), request_id)
        return self.db.execute_update(sql, params)
    
    def reject_request(self, request_id, processed_by, notes=None):
        """拒绝申请"""
        sql = """
        UPDATE material_requests SET status = 'rejected', processed_by = %s,
               processed_date = %s, notes = %s, updated_at = %s
        WHERE id = %s
        """
        params = (processed_by, datetime.now().date(), notes, datetime.now(), request_id)
        return self.db.execute_update(sql, params)

    def get_requests_by_material(self, material_id, status=None):
        """根据物资ID获取申请记录"""
        sql = "SELECT * FROM material_requests WHERE material_id = %s"
        params = [material_id]

        if status:
            sql += " AND status = %s"
            params.append(status)

        sql += " ORDER BY request_date DESC"
        return self.db.execute_query(sql, params)

    def delete_allocations_by_material(self, material_id):
        """删除物资的所有分配记录"""
        sql = "DELETE FROM material_allocations WHERE material_id = %s"
        return self.db.execute_update(sql, (material_id,))

    def delete_allocation(self, allocation_id):
        """删除分配记录"""
        sql = "DELETE FROM material_allocations WHERE id = %s"
        return self.db.execute_update(sql, (allocation_id,))

    def get_allocation_by_id(self, allocation_id):
        """根据ID获取分配记录"""
        sql = "SELECT * FROM material_allocations WHERE id = %s"
        result = self.db.execute_query(sql, (allocation_id,))
        if result:
            return MaterialAllocation.from_dict(result[0])
        return None
