import requests
import json
import logging
from typing import Dict, List, Optional
from config import Config

class ChatbotService:
    """AI聊天机器人服务，使用DeepSeek API"""

    def __init__(self, api_key: str = None):
        self.api_key = api_key or Config.DEEPSEEK_API_KEY
        self.base_url = "https://api.deepseek.com/v1/chat/completions"
        self.model = "deepseek-chat"
        self.logger = logging.getLogger(__name__)
        
        # 系统提示词，定义机器人的角色和能力
        self.system_prompt = """你是物资管理系统的智能助手，名字叫"小智"。你的主要职责是：

1. 帮助用户了解物资管理系统的功能和使用方法
2. 回答关于物资申请、审批、分配等流程的问题
3. 提供系统操作指导和故障排除建议
4. 解答物资管理相关的业务问题
5. 协助用户更好地使用系统功能

请用友好、专业的语气回答用户问题，如果遇到不确定的问题，请诚实地说明并建议用户联系系统管理员。
回答要简洁明了，重点突出，适合在聊天界面中显示。"""

    def chat(self, message: str, conversation_history: List[Dict] = None) -> Dict:
        """
        与AI聊天机器人对话
        
        Args:
            message: 用户输入的消息
            conversation_history: 对话历史记录
            
        Returns:
            包含回复内容和状态的字典
        """
        try:
            # 构建消息列表
            messages = [{"role": "system", "content": self.system_prompt}]
            
            # 添加对话历史（最多保留最近10轮对话）
            if conversation_history:
                messages.extend(conversation_history[-20:])  # 保留最近20条消息（10轮对话）
            
            # 添加当前用户消息
            messages.append({"role": "user", "content": message})
            
            # 准备API请求
            headers = {
                "Authorization": f"Bearer {self.api_key}",
                "Content-Type": "application/json"
            }
            
            data = {
                "model": self.model,
                "messages": messages,
                "max_tokens": 1000,
                "temperature": 0.7,
                "stream": False
            }
            
            # 检查是否使用演示模式（API密钥为默认值时）
            if self.api_key == "sk-your-deepseek-api-key-here":
                # 演示模式：返回模拟回复
                ai_reply = self._get_demo_reply(message)
                return {
                    "success": True,
                    "reply": ai_reply,
                    "error": None
                }

            # 发送请求到真实API
            response = requests.post(
                self.base_url,
                headers=headers,
                json=data,
                timeout=30
            )

            if response.status_code == 200:
                result = response.json()
                ai_reply = result["choices"][0]["message"]["content"]

                return {
                    "success": True,
                    "reply": ai_reply,
                    "error": None
                }
            else:
                self.logger.error(f"DeepSeek API错误: {response.status_code} - {response.text}")
                return {
                    "success": False,
                    "reply": "抱歉，AI服务暂时不可用，请稍后再试。",
                    "error": f"API错误: {response.status_code}"
                }
                
        except requests.exceptions.Timeout:
            self.logger.error("DeepSeek API请求超时")
            return {
                "success": False,
                "reply": "抱歉，响应超时，请稍后再试。",
                "error": "请求超时"
            }
        except requests.exceptions.RequestException as e:
            self.logger.error(f"DeepSeek API请求异常: {e}")
            return {
                "success": False,
                "reply": "抱歉，网络连接异常，请检查网络后重试。",
                "error": f"网络异常: {str(e)}"
            }
        except Exception as e:
            self.logger.error(f"聊天机器人服务异常: {e}")
            return {
                "success": False,
                "reply": "抱歉，服务出现异常，请联系系统管理员。",
                "error": f"服务异常: {str(e)}"
            }

    def _get_demo_reply(self, message: str) -> str:
        """演示模式的回复生成"""
        message_lower = message.lower()

        # 物资申请相关问题
        if any(keyword in message_lower for keyword in ['申请', '如何申请', '怎么申请']):
            return """📝 物资申请流程：

1. **登录系统** - 使用您的账号登录物资管理系统
2. **浏览物资** - 在"物资管理"页面查看可申请的物资
3. **提交申请** - 点击需要的物资，填写申请数量和用途
4. **等待审批** - 管理员会审核您的申请
5. **查看结果** - 在"申请状态"中查看审批结果

💡 小贴士：申请时请详细说明使用用途，有助于提高审批通过率！"""

        # 申请状态查询
        elif any(keyword in message_lower for keyword in ['状态', '查询', '进度']):
            return """🔍 查询申请状态：

您可以通过以下方式查看申请状态：
• 点击导航栏"分配记录"查看所有申请
• 申请状态包括：待审核、已批准、已拒绝、已分配
• 如有疑问，可联系管理员了解详情

📊 状态说明：
- 🟡 待审核：申请已提交，等待管理员处理
- 🟢 已批准：申请通过，等待物资分配
- 🔴 已拒绝：申请未通过，可查看拒绝原因
- 🔵 已分配：物资已分配完成"""

        # 系统功能介绍
        elif any(keyword in message_lower for keyword in ['功能', '介绍', '帮助', '使用']):
            return """🎯 系统主要功能：

**用户功能：**
• 📋 物资查询 - 浏览可申请的物资信息
• 📝 申请提交 - 提交物资申请
• 📊 状态跟踪 - 查看申请处理进度

**管理功能：**
• 🏗️ 物资管理 - 添加、修改、删除物资
• ✅ 申请审批 - 审核用户申请
• 📦 分配管理 - 处理物资分配
• 📈 统计报表 - 查看使用统计

**智能功能：**
• 🤖 AI推荐 - 个性化物资推荐
• ⚠️ 库存预警 - 自动库存提醒
• 📊 数据分析 - 智能数据洞察"""

        # 联系管理员
        elif any(keyword in message_lower for keyword in ['管理员', '联系', '客服']):
            return """👨‍💼 联系管理员：

如需人工帮助，您可以：
• 在系统内查看管理员联系方式
• 通过系统通知功能发送消息
• 拨打技术支持热线

⏰ 工作时间：周一至周五 9:00-18:00
📧 邮箱支持：<EMAIL>
📞 电话支持：400-123-4567

我会尽力帮助您解决问题，如果我无法解答，建议您联系管理员获得更专业的帮助。"""

        # 默认回复
        else:
            return f"""您好！我是物资管理系统的智能助手小智 🤖

我注意到您询问："{message}"

我可以帮助您：
• 了解物资申请流程
• 查询申请状态
• 介绍系统功能
• 解答使用问题

请告诉我您具体需要什么帮助，或者点击下方的快速回复按钮！"""

    def get_quick_replies(self) -> List[Dict]:
        """获取快速回复选项"""
        return [
            {"text": "如何申请物资？", "value": "请告诉我如何申请物资"},
            {"text": "申请状态查询", "value": "我想查询申请状态"},
            {"text": "系统使用帮助", "value": "请介绍系统的主要功能"},
            {"text": "联系管理员", "value": "如何联系系统管理员"},
        ]

    def get_system_info(self) -> str:
        """获取系统信息介绍"""
        return """欢迎使用物资管理系统！我是您的智能助手小智。

🔧 主要功能：
• 物资查询和申请
• 申请状态跟踪
• 管理员审批和分配
• 智能推荐和预警

❓ 常见问题：
• 如何申请物资？
• 申请被拒绝怎么办？
• 如何查看申请进度？

有任何问题都可以问我哦！"""

# 创建全局聊天机器人实例
chatbot_service = ChatbotService()
