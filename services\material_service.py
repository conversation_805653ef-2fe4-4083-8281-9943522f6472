from dao.material_dao import MaterialDAO
from dao.allocation_dao import AllocationDAO, RequestDAO
from dao.user_dao import UserDAO
from dao.department_dao import DepartmentDAO
from models.material import Material
from models.allocation import MaterialAllocation, MaterialRequest
from datetime import datetime, date

class MaterialService:
    def __init__(self):
        self.material_dao = MaterialDAO()
        self.allocation_dao = AllocationDAO()
        self.request_dao = RequestDAO()
        self.user_dao = UserDAO()
        self.department_dao = DepartmentDAO()
    
    def create_material(self, material_data, user):
        """创建物资"""
        if not user.is_admin():
            raise PermissionError("只有管理员可以创建物资")
        
        material = Material.from_dict(material_data)
        return self.material_dao.create_material(material)
    
    def get_material_list(self, user, category=None, keyword=None, min_price=None, max_price=None, department_id=None):
        """获取物资列表"""
        if keyword or min_price or max_price:
            materials = self.material_dao.search_materials(keyword, category, min_price, max_price)
        elif category:
            materials = self.material_dao.get_materials_by_category(category)
        else:
            materials = self.material_dao.get_all_materials()

        # 如果指定了科室ID（仅管理员可用），按科室过滤
        if user.is_admin() and department_id:
            allocated_materials = self._get_department_materials(department_id)
            allocated_material_ids = {item['material_id'] for item in allocated_materials}
            materials = [m for m in materials if m.id in allocated_material_ids]
        # 如果是普通员工，只显示已分配给其科室的物资
        elif not user.is_admin():
            allocated_materials = self._get_department_materials(user.department_id)
            allocated_material_ids = {item['material_id'] for item in allocated_materials}
            materials = [m for m in materials if m.id in allocated_material_ids]

        return [material.to_dict() for material in materials]
    
    def get_material_detail(self, material_id, user):
        """获取物资详情"""
        material = self.material_dao.get_material_by_id(material_id)
        if not material:
            return None
        
        # 检查权限
        if not user.is_admin():
            # 普通员工只能查看本科室的物资
            allocated_materials = self._get_department_materials(user.department_id)
            allocated_material_ids = {item['material_id'] for item in allocated_materials}
            if material.id not in allocated_material_ids:
                raise PermissionError("无权查看此物资")
        
        return material.to_dict()
    
    def update_material(self, material_id, material_data, user):
        """更新物资信息"""
        if not user.is_admin():
            raise PermissionError("只有管理员可以更新物资")
        
        material = self.material_dao.get_material_by_id(material_id)
        if not material:
            raise ValueError("物资不存在")
        
        # 更新物资信息
        for key, value in material_data.items():
            if hasattr(material, key):
                setattr(material, key, value)
        
        return self.material_dao.update_material(material)
    
    def scrap_material(self, material_id, reason, user):
        """报废物资"""
        if not user.is_admin():
            raise PermissionError("只有管理员可以报废物资")
        
        material = self.material_dao.get_material_by_id(material_id)
        if not material:
            raise ValueError("物资不存在")
        
        if material.is_fixed_asset():
            # 固定资产报废需要审核
            return self.material_dao.scrap_material(material_id, reason)
        else:
            # 耗材直接标记为消耗
            return self.material_dao.update_remaining_quantity(material_id, -material.remaining_quantity)
    
    def allocate_material(self, material_id, department_id, user_id, quantity, notes, allocated_by):
        """分配物资"""
        if not self.user_dao.get_user_by_id(allocated_by).is_admin():
            raise PermissionError("只有管理员可以分配物资")
        
        material = self.material_dao.get_material_by_id(material_id)
        if not material:
            raise ValueError("物资不存在")
        
        if material.remaining_quantity < quantity:
            raise ValueError("物资数量不足")
        
        # 创建分配记录
        allocation = MaterialAllocation(
            material_id=material_id,
            department_id=department_id,
            user_id=user_id,
            allocated_by=allocated_by,
            quantity=quantity,
            notes=notes
        )
        
        # 更新物资剩余数量
        self.material_dao.update_remaining_quantity(material_id, -quantity)
        
        return self.allocation_dao.create_allocation(allocation)
    
    def request_material(self, material_id, quantity, reason, user):
        """申请物资"""
        material = self.material_dao.get_material_by_id(material_id)
        if not material:
            raise ValueError("物资不存在")
        
        if material.remaining_quantity < quantity:
            raise ValueError("物资数量不足")
        
        request = MaterialRequest(
            material_id=material_id,
            user_id=user.id,
            department_id=user.department_id,
            quantity=quantity,
            reason=reason
        )
        
        return self.request_dao.create_request(request)
    
    def get_allocation_history(self, user, department_id=None):
        """获取分配历史"""
        if user.is_admin():
            if department_id:
                return self.allocation_dao.get_allocations_by_department(department_id)
            else:
                return self.allocation_dao.get_all_allocations()
        else:
            # 普通员工可以查看自己科室的分配记录
            if department_id:
                # 检查权限：普通员工只能查看自己科室的记录
                if department_id != user.department_id:
                    raise PermissionError("无权查看其他科室的分配记录")
                return self.allocation_dao.get_allocations_by_department(department_id)
            else:
                # 默认显示自己科室的所有分配记录
                return self.allocation_dao.get_allocations_by_department(user.department_id)

    def delete_material(self, material_id, user):
        """删除物资"""
        material = self.material_dao.get_material_by_id(material_id)
        if not material:
            raise ValueError("物资不存在")

        # 检查权限：普通用户只能删除分配给自己科室的物资，管理员可以删除任何物资
        if not user.is_admin():
            # 检查物资是否分配给用户所在科室
            allocated_materials = self._get_department_materials(user.department_id)
            allocated_material_ids = {item['material_id'] for item in allocated_materials}
            if material.id not in allocated_material_ids:
                raise PermissionError("无权删除此物资")

        # 检查是否有未完成的申请
        pending_requests = self.request_dao.get_requests_by_material(material_id, status='pending')
        if pending_requests:
            raise ValueError("该物资有待审核的申请，无法删除")

        # 删除相关的分配记录
        self.allocation_dao.delete_allocations_by_material(material_id)

        # 删除物资
        return self.material_dao.delete_material(material_id)

    def update_material(self, material_id, material_data, user):
        """更新物资信息"""
        if not user.is_admin():
            raise PermissionError("只有管理员可以修改物资信息")

        material = self.material_dao.get_material_by_id(material_id)
        if not material:
            raise ValueError("物资不存在")

        # 更新物资信息
        for key, value in material_data.items():
            if hasattr(material, key):
                setattr(material, key, value)

        return self.material_dao.update_material(material)

    def reject_request(self, request_id, user, notes=None):
        """拒绝申请"""
        if not user.is_admin():
            raise PermissionError("只有管理员可以拒绝申请")

        return self.request_dao.reject_request(request_id, user.id, notes)

    def import_materials_from_excel(self, file, user):
        """从Excel文件批量导入物资"""
        if not user.is_admin():
            raise PermissionError("只有管理员可以批量导入物资")

        try:
            import openpyxl
            from datetime import datetime
        except ImportError:
            raise ImportError("请安装openpyxl库：pip install openpyxl")

        result = {
            'success_count': 0,
            'error_count': 0,
            'errors': []
        }

        try:
            # 读取Excel文件
            workbook = openpyxl.load_workbook(file)
            worksheet = workbook.active

            # 跳过标题行，从第2行开始
            for row_num, row in enumerate(worksheet.iter_rows(min_row=2, values_only=True), start=2):
                try:
                    # 检查是否为空行
                    if not any(row):
                        continue

                    # 解析行数据
                    name = row[0] if row[0] else None
                    model = row[1] if len(row) > 1 and row[1] else None
                    category = row[2] if len(row) > 2 and row[2] else 'consumable'
                    price = float(row[3]) if len(row) > 3 and row[3] else 0
                    purchase_date = row[4] if len(row) > 4 and row[4] else datetime.now().date()
                    supplier = row[5] if len(row) > 5 and row[5] else None
                    purchase_amount = float(row[6]) if len(row) > 6 and row[6] else 0
                    quantity = int(row[7]) if len(row) > 7 and row[7] else 1
                    description = row[8] if len(row) > 8 and row[8] else None

                    # 验证必填字段
                    if not name:
                        result['errors'].append({
                            'row': row_num,
                            'message': '物资名称不能为空'
                        })
                        result['error_count'] += 1
                        continue

                    if price <= 0:
                        result['errors'].append({
                            'row': row_num,
                            'message': '单价必须大于0'
                        })
                        result['error_count'] += 1
                        continue

                    if quantity <= 0:
                        result['errors'].append({
                            'row': row_num,
                            'message': '数量必须大于0'
                        })
                        result['error_count'] += 1
                        continue

                    # 验证类别
                    if category not in ['fixed_asset', 'consumable']:
                        category = 'consumable'

                    # 处理日期
                    if isinstance(purchase_date, str):
                        try:
                            purchase_date = datetime.strptime(purchase_date, '%Y-%m-%d').date()
                        except ValueError:
                            purchase_date = datetime.now().date()
                    elif not isinstance(purchase_date, datetime.date):
                        purchase_date = datetime.now().date()

                    # 如果没有采购金额，自动计算
                    if purchase_amount <= 0:
                        purchase_amount = price * quantity

                    # 创建物资数据
                    material_data = {
                        'name': name,
                        'model': model,
                        'category': category,
                        'price': price,
                        'purchase_date': purchase_date,
                        'supplier': supplier,
                        'purchase_amount': purchase_amount,
                        'quantity': quantity,
                        'description': description
                    }

                    # 创建物资
                    self.create_material(material_data, user)
                    result['success_count'] += 1

                except Exception as e:
                    result['errors'].append({
                        'row': row_num,
                        'message': str(e)
                    })
                    result['error_count'] += 1

        except Exception as e:
            raise Exception(f"读取Excel文件失败：{str(e)}")

        return result

    def delete_allocation(self, allocation_id, user):
        """删除分配记录"""
        if not user.is_admin():
            raise PermissionError("只有管理员可以删除分配记录")

        # 检查分配记录是否存在
        allocation = self.allocation_dao.get_allocation_by_id(allocation_id)
        if not allocation:
            raise ValueError("分配记录不存在")

        # 删除分配记录时，需要恢复物资的剩余数量
        material = self.material_dao.get_material_by_id(allocation.material_id)
        if material:
            material.remaining_quantity += allocation.quantity
            self.material_dao.update_material(material)

        # 删除分配记录
        return self.allocation_dao.delete_allocation(allocation_id)
    
    def get_pending_requests(self, user):
        """获取待审核申请"""
        if not user.is_admin():
            raise PermissionError("只有管理员可以查看待审核申请")
        
        return self.request_dao.get_pending_requests()
    
    def approve_request(self, request_id, user, notes=None):
        """批准申请"""
        if not user.is_admin():
            raise PermissionError("只有管理员可以批准申请")
        
        request = self.request_dao.get_request_by_id(request_id)
        if not request:
            raise ValueError("申请不存在")
        
        if not request.is_pending():
            raise ValueError("申请已处理")
        
        # 批准申请
        self.request_dao.approve_request(request_id, user.id, notes)
        
        # 自动创建分配记录
        return self.allocate_material(
            request.material_id,
            request.department_id,
            request.user_id,
            request.quantity,
            f"申请批准: {notes}" if notes else "申请批准",
            user.id
        )
    
    def reject_request(self, request_id, user, notes=None):
        """拒绝申请"""
        if not user.is_admin():
            raise PermissionError("只有管理员可以拒绝申请")
        
        request = self.request_dao.get_request_by_id(request_id)
        if not request:
            raise ValueError("申请不存在")
        
        if not request.is_pending():
            raise ValueError("申请已处理")
        
        return self.request_dao.reject_request(request_id, user.id, notes)
    
    def _get_department_materials(self, department_id):
        """获取科室的物资分配记录"""
        return self.allocation_dao.get_allocations_by_department(department_id)
