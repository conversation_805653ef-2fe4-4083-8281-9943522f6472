#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import requests
import json

def test_chatbot_api():
    """测试聊天机器人API"""
    
    # 测试URL
    base_url = "http://127.0.0.1:5000"
    
    print("🤖 测试AI聊天机器人API...")
    print("=" * 50)
    
    # 首先需要登录获取session
    login_url = f"{base_url}/auth/login"
    chat_url = f"{base_url}/api/chat"
    
    # 创建session
    session = requests.Session()
    
    try:
        # 1. 获取登录页面
        print("1. 获取登录页面...")
        response = session.get(login_url)
        if response.status_code == 200:
            print("✓ 登录页面访问成功")
        else:
            print(f"✗ 登录页面访问失败: {response.status_code}")
            return
        
        # 2. 尝试登录
        print("2. 尝试登录...")
        login_data = {
            'username': 'admin',
            'password': 'admin123'
        }
        
        response = session.post(login_url, data=login_data)
        if response.status_code == 200 or response.status_code == 302:
            print("✓ 登录成功")
        else:
            print(f"✗ 登录失败: {response.status_code}")
            return
        
        # 3. 测试聊天API
        print("3. 测试聊天API...")
        
        test_messages = [
            "你好",
            "如何申请物资？",
            "请介绍系统的主要功能",
            "我想查询申请状态"
        ]
        
        for i, message in enumerate(test_messages, 1):
            print(f"\n测试消息 {i}: {message}")
            
            chat_data = {
                'message': message,
                'history': []
            }
            
            response = session.post(
                chat_url,
                json=chat_data,
                headers={'Content-Type': 'application/json'}
            )
            
            if response.status_code == 200:
                result = response.json()
                if result.get('success'):
                    reply = result.get('reply', '')
                    print(f"✓ AI回复: {reply[:100]}...")
                else:
                    print(f"✗ API返回错误: {result.get('error', '未知错误')}")
            else:
                print(f"✗ 请求失败: {response.status_code}")
                print(f"响应内容: {response.text[:200]}")
        
        # 4. 测试快速回复API
        print("\n4. 测试快速回复API...")
        quick_replies_url = f"{base_url}/api/chat/quick-replies"
        
        response = session.get(quick_replies_url)
        if response.status_code == 200:
            result = response.json()
            if result.get('success'):
                quick_replies = result.get('quick_replies', [])
                print(f"✓ 快速回复选项: {len(quick_replies)} 个")
                for reply in quick_replies:
                    print(f"  - {reply.get('text', '')}")
            else:
                print(f"✗ 快速回复API错误: {result.get('error', '未知错误')}")
        else:
            print(f"✗ 快速回复API请求失败: {response.status_code}")
        
        print("\n" + "=" * 50)
        print("🎉 聊天机器人API测试完成！")
        
    except requests.exceptions.ConnectionError:
        print("✗ 连接失败：请确保系统正在运行 (python app.py)")
    except Exception as e:
        print(f"✗ 测试异常: {e}")

if __name__ == "__main__":
    test_chatbot_api()
