# AI聊天机器人使用说明

## 🤖 功能概述

物资管理系统现已集成AI智能助手"小智"，为用户提供24/7智能客服支持。

### ✨ 主要特性
- **智能问答**: 回答物资管理相关问题
- **操作指导**: 提供系统使用帮助
- **快速回复**: 预设常见问题快速回复
- **上下文理解**: 支持多轮对话
- **实时响应**: 即时获得AI回复

## 🚀 如何使用

### 1. 访问聊天机器人
- 登录系统后，点击导航栏中的"AI助手"
- 或直接访问: http://127.0.0.1:5000/chatbot

### 2. 开始对话
- 在输入框中输入您的问题
- 点击发送按钮或按回车键
- AI助手会立即回复您的问题

### 3. 快速回复
点击预设的快速回复按钮：
- "如何申请物资？"
- "申请状态查询"
- "系统使用帮助"
- "联系管理员"

## 💡 AI助手能力

### 📋 物资申请指导
- 详细的申请流程说明
- 申请技巧和注意事项
- 常见问题解答

### 🔍 状态查询帮助
- 申请状态含义解释
- 查询方法指导
- 进度跟踪建议

### 🛠️ 系统功能介绍
- 完整功能列表
- 使用方法说明
- 操作步骤指导

### 👨‍💼 技术支持
- 联系方式提供
- 问题分类指导
- 故障排除建议

## 🔧 技术配置

### API配置
系统支持两种模式：

#### 1. 演示模式（默认）
- 使用内置智能回复
- 无需API密钥
- 适合测试和演示

#### 2. DeepSeek API模式
配置真实的DeepSeek API：

1. **获取API密钥**
   - 访问 https://platform.deepseek.com
   - 注册账号并获取API密钥

2. **配置密钥**
   ```python
   # 方法1：修改config.py
   DEEPSEEK_API_KEY = "sk-your-actual-api-key"
   
   # 方法2：设置环境变量
   export DEEPSEEK_API_KEY="sk-your-actual-api-key"
   ```

3. **重启系统**
   ```bash
   python app.py
   ```

### 系统要求
- Python 3.8+
- Flask 2.2.5+
- requests 2.31.0+
- 网络连接（API模式）

## 📱 界面特性

### 🎨 用户体验
- **响应式设计**: 适配各种屏幕尺寸
- **实时动画**: 消息发送和接收动画
- **加载提示**: 思考过程可视化
- **消息历史**: 保留对话记录

### 🎯 交互设计
- **用户消息**: 右侧蓝色气泡
- **AI回复**: 左侧白色气泡
- **时间戳**: 每条消息显示发送时间
- **头像标识**: 区分用户和AI消息

## 🔒 安全与隐私

### 数据保护
- 对话记录仅在当前会话保存
- 不存储敏感个人信息
- API通信使用HTTPS加密

### 访问控制
- 需要登录系统才能使用
- 基于用户权限的功能访问
- 会话超时自动退出

## 🚨 故障排除

### 常见问题

#### 1. AI助手无响应
**解决方案:**
- 检查网络连接
- 确认API密钥配置
- 查看系统日志

#### 2. 回复内容异常
**解决方案:**
- 重新加载页面
- 清除浏览器缓存
- 联系系统管理员

#### 3. 页面加载失败
**解决方案:**
- 确认系统正在运行
- 检查端口5000是否可用
- 验证用户登录状态

### 日志查看
```bash
# 查看系统日志
tail -f logs/agent.log

# 查看Flask日志
# 在终端中查看app.py运行输出
```

## 📈 未来规划

### 功能扩展
- **语音交互**: 支持语音输入和输出
- **文件上传**: 支持图片和文档分析
- **多语言**: 支持英文等多语言
- **个性化**: 基于用户习惯的个性化回复

### 智能升级
- **深度学习**: 基于使用数据优化回复
- **知识库**: 集成企业内部知识库
- **工作流**: 支持复杂业务流程自动化

## 📞 技术支持

如需帮助，请联系：
- **系统管理员**: <EMAIL>
- **技术支持**: 400-123-4567
- **在线文档**: http://docs.company.com

---

**注意**: 当前版本为演示版本，使用内置智能回复。如需使用完整AI功能，请配置DeepSeek API密钥。
