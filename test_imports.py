#!/usr/bin/env python3
# -*- coding: utf-8 -*-

print("开始测试导入...")

try:
    from flask import Flask
    print("✓ Flask 导入成功")
except ImportError as e:
    print(f"✗ Flask 导入失败: {e}")

try:
    from config import Config
    print("✓ Config 导入成功")
except ImportError as e:
    print(f"✗ Config 导入失败: {e}")

try:
    from services.chatbot_service import chatbot_service
    print("✓ ChatbotService 导入成功")
except ImportError as e:
    print(f"✗ ChatbotService 导入失败: {e}")

try:
    from controllers.chatbot_controller import chatbot_bp
    print("✓ ChatbotController 导入成功")
except ImportError as e:
    print(f"✗ ChatbotController 导入失败: {e}")

try:
    from controllers.auth_controller import auth_bp
    print("✓ AuthController 导入成功")
except ImportError as e:
    print(f"✗ AuthController 导入失败: {e}")

try:
    from controllers.material_controller import material_bp
    print("✓ MaterialController 导入成功")
except ImportError as e:
    print(f"✗ MaterialController 导入失败: {e}")

try:
    from controllers.report_controller import report_bp
    print("✓ ReportController 导入成功")
except ImportError as e:
    print(f"✗ ReportController 导入失败: {e}")

try:
    from controllers.agent_controller import agent_bp
    print("✓ AgentController 导入成功")
except ImportError as e:
    print(f"✗ AgentController 导入失败: {e}")

print("导入测试完成！")
