#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
为users表添加email和phone字段
"""

import pymysql

def add_user_fields():
    """为users表添加缺失的字段"""
    config = {
        'host': 'localhost',
        'user': 'root',
        'password': 'lax217652',
        'database': 'goods',
        'charset': 'utf8mb4'
    }
    
    try:
        # 连接数据库
        connection = pymysql.connect(**config)
        cursor = connection.cursor()
        
        print("检查users表当前结构...")
        cursor.execute("DESCRIBE users")
        columns = cursor.fetchall()
        existing_columns = [col[0] for col in columns]
        
        print("当前字段:", existing_columns)
        
        # 检查并添加email字段
        if 'email' not in existing_columns:
            print("添加email字段...")
            cursor.execute("ALTER TABLE users ADD COLUMN email VARCHAR(100) COMMENT '邮箱'")
            print("✅ email字段添加成功")
        else:
            print("email字段已存在")
        
        # 检查并添加phone字段
        if 'phone' not in existing_columns:
            print("添加phone字段...")
            cursor.execute("ALTER TABLE users ADD COLUMN phone VARCHAR(20) COMMENT '电话'")
            print("✅ phone字段添加成功")
        else:
            print("phone字段已存在")
        
        connection.commit()
        
        print("\n检查更新后的表结构...")
        cursor.execute("DESCRIBE users")
        columns = cursor.fetchall()
        print("更新后的字段:")
        for col in columns:
            print(f"  {col[0]}: {col[1]}")
        
        print("\n✅ 用户表字段更新完成！")
        
    except Exception as e:
        print(f"❌ 更新失败: {e}")
    finally:
        if 'connection' in locals():
            connection.close()

if __name__ == '__main__':
    add_user_fields()
